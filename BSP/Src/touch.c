/**
 ******************************************************************************
 * @file    touch.c
 * @brief   GT1158/GT9xx 触摸芯片驱动实现文件
 * <AUTHOR> Project Team
 * @version V3.1
 * @date    2025-01-25
 ******************************************************************************
 * @attention
 *
 * 功能特性：
 * - 支持GT1158/GT9xx系列触摸芯片
 * - 支持多点触摸（最多5个触摸点）
 * - 支持手势识别（单击、长按、滑动等）
 * - 使用I2C2接口进行通信，带重试机制
 * - 硬件连接：I2C2(PH4-SCL, PH5-SDA), RESET(PI8), INT(PD13)
 * - 屏幕分辨率：800x480（可配置）
 *
 ******************************************************************************
 */

/* ========================================================================================
 * 头文件包含
 * ======================================================================================== */
#include "touch.h"
#include <string.h>
#include <stdio.h>
#include <stdlib.h>
#include <math.h>
#include "lcd.h"
#include "i2c.h"

/* ========================================================================================
 * 私有宏定义
 * ======================================================================================== */

/* 内部使用的常量定义 */
#define TOUCH_RESET_DELAY_MS        20      ///< 复位延时时间
#define TOUCH_STARTUP_DELAY_MS      55      ///< 启动延时时间
#define TOUCH_I2C_RETRY_DELAY_MS    1       ///< I2C重试延时时间

/* ========================================================================================
 * 私有变量定义
 * ======================================================================================== */
/* GT1158配置结构体实例 */
gt1158_config_t g_gt1158_config = {
    .screen_width = LCD_PANEL_WIDTH,
    .screen_height = LCD_PANEL_HEIGHT,
    .orientation = SCREEN_ORIENTATION_LANDSCAPE,
    .gesture_detection_enable = 1,      ///< 默认启用手势检测
    .multi_touch_enable = 1,            ///< 默认启用多点触摸
    .is_initialized = 0                 ///< 初始化标志
};

/* GT1158触摸数据结构体实例 */
gt1158_touch_data_t g_gt1158_touch_data = {
    .point_count = 0,
    .status_reg = 0,
    .points = {0},
    .timestamp_ms = 0
};

/* 触摸状态管理实例 */
touch_state_manager_t g_touch_state = {
    .current_state = TOUCH_STATE_IDLE,
    .press_x = 0,
    .press_y = 0,
    .current_x = 0,
    .current_y = 0,
    .press_timestamp_ms = 0,
    .release_timestamp_ms = 0,
    
    .is_moving = 0,
    .long_press_triggered = 0
};

/* 手势事件实例 */
touch_gesture_event_t g_gesture_event = {
    .gesture = TOUCH_GESTURE_NONE,
    .x = 0,
    .y = 0,
    .start_x = 0,
    .start_y = 0,
    .end_x = 0,
    .end_y = 0,
    .duration_ms = 0,
    .timestamp_ms = 0
};

/* 私有静态变量 */
static volatile uint8_t s_touch_interrupt_flag = 0;                ///< 触摸中断标志（volatile确保中断安全）
static uint16_t s_previous_x[GT1158_MAX_TOUCH_POINTS] = {0};       ///< 前一次触摸X坐标
static uint16_t s_previous_y[GT1158_MAX_TOUCH_POINTS] = {0};       ///< 前一次触摸Y坐标
static uint8_t s_previous_touch_state = 0;                         ///< 前一次触摸状态


/* 全局变量，供外部访问 */
volatile uint8_t g_touch_event_flag = 0;                           ///< 触摸事件标志（供主循环使用）

/* ========================================================================================
 * 私有函数声明
 * ======================================================================================== */

/* 硬件控制函数 */
static void Touch_HardwareReset(void);
static touch_result_t Touch_CheckDevice(void);

/* 数据处理函数 */
static touch_result_t Touch_ScanRawData(void);
static void Touch_ProcessRawData(void);

/* 手势检测函数 */
static touch_gesture_type_t Touch_DetectGesture(void);
static touch_gesture_type_t Touch_DetectSwipeGesture(void);
static void Touch_ResetGestureState(void);

/* 工具函数 */
static uint8_t Touch_IsValidCoordinate(uint16_t x, uint16_t y);
static uint16_t Touch_CalculateDistance(uint16_t x1, uint16_t y1, uint16_t x2, uint16_t y2);
static void Touch_TransformCoordinates(uint16_t *x, uint16_t *y);
static void Touch_GetCurrentScreenSize(uint16_t *width, uint16_t *height);

/* ========================================================================================
 * 函数实现
 * ======================================================================================== */

/* ----------------------------------------------------------------------------------------
 * 1. I2C通信函数实现
 * ---------------------------------------------------------------------------------------- */

/**
 * @brief  向GT1158芯片写入数据
 * @note   带重试机制的I2C写入函数，提高通信可靠性
 * @param  device_addr I2C设备地址
 * @param  data_buffer 数据缓冲区指针
 * @param  data_length 数据长度（字节）
 * @retval int32_t 写入结果（0-成功，负数-错误代码）
 */
int32_t Touch_I2C_Write(uint8_t device_addr, uint8_t *data_buffer, int32_t data_length)
{
    HAL_StatusTypeDef hal_status;
    uint8_t retry_count = 0;

    /* 参数有效性检查 */
    if (data_buffer == NULL || data_length <= 0)
    {
        return -1;
    }

    /* 重试机制，提高通信可靠性 */
    while (retry_count < GT1158_I2C_RETRY_COUNT)
    {
        hal_status = HAL_I2C_Master_Transmit(&hi2c2, device_addr, data_buffer,
                                           data_length, GT1158_I2C_TIMEOUT_MS);

        if (hal_status == HAL_OK)
        {
            return 0;
        }

        retry_count++;

        /* 短暂延时后重试 */
        HAL_Delay(TOUCH_I2C_RETRY_DELAY_MS);
    }

    return -1;
}

/**
 * @brief  从GT1158芯片读取数据
 * @note   带重试机制的I2C读取函数，提高通信可靠性
 * @param  device_addr I2C设备地址
 * @param  data_buffer 数据缓冲区指针
 * @param  data_length 数据长度（字节）
 * @retval int32_t 读取结果（0-成功，负数-错误代码）
 */
int32_t Touch_I2C_Read(uint8_t device_addr, uint8_t *data_buffer, int32_t data_length)
{
    HAL_StatusTypeDef hal_status;
    uint8_t retry_count = 0;

    /* 参数有效性检查 */
    if (data_buffer == NULL || data_length <= 0)
    {
        return -1;
    }

    /* 重试机制，提高通信可靠性 */
    while (retry_count < GT1158_I2C_RETRY_COUNT)
    {
        hal_status = HAL_I2C_Master_Receive(&hi2c2, device_addr, data_buffer,
                                          data_length, GT1158_I2C_TIMEOUT_MS);

        if (hal_status == HAL_OK)
        {
            return 0;
        }

        retry_count++;

        /* 短暂延时后重试 */
        HAL_Delay(TOUCH_I2C_RETRY_DELAY_MS);
    }

    return -1;
}

/* ----------------------------------------------------------------------------------------
 * 硬件控制函数实现
 * ---------------------------------------------------------------------------------------- */

/**
 * @brief  执行GT1158触摸芯片硬件复位
 * @note   按照GT1158规范执行复位时序，确保芯片正确初始化
 * @param  None
 * @retval None
 */
static void Touch_HardwareReset(void)
{
    GPIO_InitTypeDef gpio_init_struct;

    /* 第一步：配置INT管脚为输出模式 */
    gpio_init_struct.Pin = GT1158_INT_PIN;
    gpio_init_struct.Mode = GPIO_MODE_OUTPUT_PP;
    gpio_init_struct.Speed = GPIO_SPEED_FREQ_HIGH;
    gpio_init_struct.Pull = GPIO_NOPULL;
    HAL_GPIO_Init(GT1158_INT_PORT, &gpio_init_struct);

    /* 第二步：配置RESET管脚为输出模式 */
    gpio_init_struct.Pin = GT1158_RST_PIN;
    gpio_init_struct.Mode = GPIO_MODE_OUTPUT_PP;
    gpio_init_struct.Speed = GPIO_SPEED_FREQ_HIGH;
    gpio_init_struct.Pull = GPIO_NOPULL;
    HAL_GPIO_Init(GT1158_RST_PORT, &gpio_init_struct);

    /* 第三步：执行GT1158标准复位时序 */
    HAL_GPIO_WritePin(GT1158_INT_PORT, GT1158_INT_PIN, GPIO_PIN_RESET);
    HAL_GPIO_WritePin(GT1158_RST_PORT, GT1158_RST_PIN, GPIO_PIN_RESET);
    HAL_Delay(TOUCH_RESET_DELAY_MS);

    HAL_GPIO_WritePin(GT1158_RST_PORT, GT1158_RST_PIN, GPIO_PIN_SET);
    HAL_Delay(6);  /* GT1158规范要求的延时 */

    HAL_GPIO_WritePin(GT1158_INT_PORT, GT1158_INT_PIN, GPIO_PIN_SET);
    HAL_Delay(TOUCH_STARTUP_DELAY_MS);

    /* 第四步：将INT管脚重新配置为输入模式（用于中断检测） */
    gpio_init_struct.Pin = GT1158_INT_PIN;
    gpio_init_struct.Mode = GPIO_MODE_INPUT;
    gpio_init_struct.Speed = GPIO_SPEED_FREQ_HIGH;
    gpio_init_struct.Pull = GPIO_NOPULL;
    HAL_GPIO_Init(GT1158_INT_PORT, &gpio_init_struct);
}

/**
 * @brief  检测GT1158设备是否存在并正常工作
 * @note   通过读取产品ID寄存器来验证设备连接
 * @param  None
 * @retval touch_result_t 检测结果
 */
static touch_result_t Touch_CheckDevice(void)
{
    uint8_t product_id_cmd[2] = {GT1158_REG_PRODUCT_ID >> 8, GT1158_REG_PRODUCT_ID & 0xFF};
    uint8_t product_id_data[4] = {0};
    int32_t i2c_result;

    /* 发送产品ID寄存器地址 */
    i2c_result = Touch_I2C_Write(GT1158_I2C_ADDR, product_id_cmd, 2);
    if (i2c_result != 0)
    {
        return TOUCH_RESULT_I2C_ERROR;
    }

    /* 读取产品ID数据 */
    i2c_result = Touch_I2C_Read(GT1158_I2C_ADDR, product_id_data, 4);
    if (i2c_result != 0)
    {
        return TOUCH_RESULT_I2C_ERROR;
    }

    /* 验证产品ID（GT1158的产品ID通常以"11"开头） */
    if (product_id_data[0] == '1' && product_id_data[1] == '1')
    {
        return TOUCH_RESULT_OK;
    }
    else
    {
        return TOUCH_RESULT_ERROR;
    }
}

/* ----------------------------------------------------------------------------------------
 * 初始化和配置函数实现
 * ---------------------------------------------------------------------------------------- */

/**
 * @brief  根据LCD配置自动计算触摸参数
 * @note   分析LCD的方向和尺寸，自动计算匹配的触摸参数
 * @param  touch_width 输出触摸宽度
 * @param  touch_height 输出触摸高度
 * @param  touch_orientation 输出触摸方向
 * @retval None
 */
static void Touch_CalculateAutoConfig(uint16_t *touch_width, uint16_t *touch_height, screen_orientation_t *touch_orientation)
{
    extern lcd_device_t g_lcd_device;

    /*
     * 坐标系统映射关系分析（基于用户提供的参考平面）：
     *
     * LCD面板物理规格：800x480（宽x高）
     *
     * 横屏模式（LCD方向1）：
     * - 显示区域：800x480
     * - 起始点(0,0)：左上角
     * - X增加方向：左上角 → 右上角（向右）
     * - Y增加方向：左上角 → 左下角（向下）
     * - LCD坐标转换：直接映射 address = panel_width * y + x
     *
     * 竖屏模式（LCD方向0）：
     * - 显示区域：480x800
     * - 起始点(0,0)：横屏状态下的左下角位置
     * - X增加方向：横屏的左下角 → 左上角（向上）
     * - Y增加方向：横屏的左下角 → 右下角（向右）
     * - LCD坐标转换：address = panel_width * (panel_height - x - 1) + y
     *
     * 触摸芯片坐标系统配置：
     * - 触摸芯片的原始坐标系统与LCD横屏模式匹配
     * - 需要根据LCD方向配置相应的触摸参数以保持坐标一致性
     */

    if (g_lcd_device.orientation == 1) {
        /* LCD横屏模式：触摸配置与LCD物理尺寸直接匹配 */
        *touch_width = LCD_PANEL_WIDTH;   /* 800 */
        *touch_height = LCD_PANEL_HEIGHT; /* 480 */
        *touch_orientation = 0;           /* 触摸方向0（无转换）与LCD横屏匹配 */
    } else {
        /* LCD竖屏模式：触摸配置需要匹配竖屏的坐标系统
         *
         * 分析竖屏坐标转换需求：
         * - LCD竖屏(0,0)位置：横屏状态下的左下角
         * - LCD竖屏X增加：横屏的左下角→左上角（向上）
         * - LCD竖屏Y增加：横屏的左下角→右下角（向右）
         *
         * 触摸芯片原始坐标（横屏基准）：
         * - 原始(0,0)：左上角
         * - 原始X增加：向右，原始Y增加：向下
         *
         * 自定义坐标转换（触摸方向1）：
         * - 竖屏X = 触摸高度 - 原始Y - 1（向上为正）
         * - 竖屏Y = 原始X（向右为正）
         *
         * 这个转换确保触摸坐标与LCD竖屏坐标系统完全匹配
         */
        *touch_width = LCD_PANEL_HEIGHT;  /* 480 */
        *touch_height = LCD_PANEL_WIDTH;  /* 800 */
        *touch_orientation = 1;           /* 触摸方向1（自定义转换）匹配竖屏坐标系统 */
    }
}

/* ----------------------------------------------------------------------------------------
 * 2. 初始化和配置函数实现
 * ---------------------------------------------------------------------------------------- */

/**
 * @brief  初始化GT1158触摸芯片（完整初始化）
 * @note   一键完成所有初始化：硬件复位、设备检测、屏幕配置、中断启用、手势检测
 *         自动根据LCD配置计算匹配的触摸参数
 * @param  None
 * @retval touch_result_t 初始化结果
 */
touch_result_t Touch_Init(void)
{
    touch_result_t result;
    uint8_t clear_status_cmd[3] = {GT1158_REG_STATUS >> 8, GT1158_REG_STATUS & 0xFF, 0x00};

    /* 第一步：执行硬件复位 */
    Touch_HardwareReset();

    /* 第二步：检测设备是否存在 */
    result = Touch_CheckDevice();
    if (result != TOUCH_RESULT_OK)
    {
        return result;
    }

    /* 第三步：清除状态寄存器 */
    if (Touch_I2C_Write(GT1158_I2C_ADDR, clear_status_cmd, 3) != 0)
    {
        return TOUCH_RESULT_I2C_ERROR;
    }

    /* 第四步：自动配置默认屏幕参数 - 根据LCD配置自动适配 */
    uint16_t touch_width, touch_height;
    screen_orientation_t touch_orientation;

    /* 根据当前LCD配置自动计算匹配的触摸参数 */
    Touch_CalculateAutoConfig(&touch_width, &touch_height, &touch_orientation);

    result = Touch_Config(touch_width, touch_height, touch_orientation);
    if (result != TOUCH_RESULT_OK)
    {
        return result;
    }

    /* 第五步：自动启用GPIO中断 */
    Touch_EnableInterrupt();

    /* 第六步：自动启用手势检测 */
    result = Touch_SetGestureDetection(1);
    if (result != TOUCH_RESULT_OK)
    {
        return result;
    }

    /* 第七步：初始化触摸数据结构 */
    memset(&g_gt1158_touch_data, 0, sizeof(gt1158_touch_data_t));

    /* 第八步：初始化手势检测状态 */
    Touch_ResetGestureState();

    /* 第九步：初始化私有变量 */
    s_touch_interrupt_flag = 0;
    memset(s_previous_x, 0, sizeof(s_previous_x));
    memset(s_previous_y, 0, sizeof(s_previous_y));
    s_previous_touch_state = 0;

    /* 第十步：标记初始化完成 */
    g_gt1158_config.is_initialized = 1;

    return TOUCH_RESULT_OK;
}

/**
 * @brief  高级初始化GT1158触摸芯片（自定义参数）
 * @note   允许用户自定义屏幕参数和功能配置
 * @param  width 屏幕宽度（像素）
 * @param  height 屏幕高度（像素）
 * @param  orientation 屏幕方向
 * @param  enable_gesture 是否启用手势检测（1-启用，0-禁用）
 * @param  enable_interrupt 是否启用GPIO中断（1-启用，0-禁用）
 * @retval touch_result_t 初始化结果
 */
touch_result_t Touch_InitAdvanced(uint16_t width, uint16_t height, screen_orientation_t orientation,
                                  uint8_t enable_gesture, uint8_t enable_interrupt)
{
    touch_result_t result;
    uint8_t clear_status_cmd[3] = {GT1158_REG_STATUS >> 8, GT1158_REG_STATUS & 0xFF, 0x00};

    /* 参数有效性检查 */
    if (width == 0 || height == 0 || orientation >= 4)
    {
        return TOUCH_RESULT_INVALID_PARAM;
    }

    /* 第一步：执行硬件复位 */
    Touch_HardwareReset();

    /* 第二步：检测设备是否存在 */
    result = Touch_CheckDevice();
    if (result != TOUCH_RESULT_OK)
    {
        return result;
    }

    /* 第三步：清除状态寄存器 */
    if (Touch_I2C_Write(GT1158_I2C_ADDR, clear_status_cmd, 3) != 0)
    {
        return TOUCH_RESULT_I2C_ERROR;
    }

    /* 第四步：配置用户指定的屏幕参数 */
    result = Touch_Config(width, height, orientation);
    if (result != TOUCH_RESULT_OK)
    {
        return result;
    }

    /* 第五步：根据用户选择启用GPIO中断 */
    if (enable_interrupt)
    {
        Touch_EnableInterrupt();
    }

    /* 第六步：根据用户选择启用手势检测 */
    if (enable_gesture)
    {
        result = Touch_SetGestureDetection(1);
        if (result != TOUCH_RESULT_OK)
        {
            return result;
        }
    }

    /* 第七步：初始化触摸数据结构 */
    memset(&g_gt1158_touch_data, 0, sizeof(gt1158_touch_data_t));

    /* 第八步：初始化手势检测状态 */
    Touch_ResetGestureState();

    /* 第九步：初始化私有变量 */
    s_touch_interrupt_flag = 0;
    memset(s_previous_x, 0, sizeof(s_previous_x));
    memset(s_previous_y, 0, sizeof(s_previous_y));
    s_previous_touch_state = 0;

    /* 第十步：标记初始化完成 */
    g_gt1158_config.is_initialized = 1;

    return TOUCH_RESULT_OK;
}

/**
 * @brief  配置触摸芯片参数
 * @note   设置屏幕分辨率、方向等参数
 * @param  width 屏幕宽度（像素）
 * @param  height 屏幕高度（像素）
 * @param  orientation 屏幕方向
 * @retval touch_result_t 配置结果
 */
touch_result_t Touch_Config(uint16_t width, uint16_t height, screen_orientation_t orientation)
{
    /* 参数有效性检查 */
    if (width == 0 || height == 0)
    {
        return TOUCH_RESULT_INVALID_PARAM;
    }

    if (orientation >= 4)  /* 只支持0-3四种方向 */
    {
        return TOUCH_RESULT_INVALID_PARAM;
    }

    /* 更新配置参数 */
    g_gt1158_config.screen_width = width;
    g_gt1158_config.screen_height = height;
    g_gt1158_config.orientation = orientation;

    return TOUCH_RESULT_OK;
}

/**
 * @brief  根据LCD当前配置重新配置触摸参数
 * @note   当LCD方向发生变化时，调用此函数重新配置触摸参数以保持坐标匹配
 * @param  None
 * @retval touch_result_t 配置结果
 */
touch_result_t Touch_ReconfigureForLCD(void)
{
    uint16_t touch_width, touch_height;
    screen_orientation_t touch_orientation;

    /* 根据当前LCD配置自动计算匹配的触摸参数 */
    Touch_CalculateAutoConfig(&touch_width, &touch_height, &touch_orientation);

    /* 应用新的触摸配置 */
    return Touch_Config(touch_width, touch_height, touch_orientation);
}

/**
 * @brief  获取当前触摸屏幕配置信息
 * @note   返回当前触摸系统的屏幕配置，支持横竖屏动态查询
 * @param  width 输出屏幕宽度指针（可为NULL）
 * @param  height 输出屏幕高度指针（可为NULL）
 * @param  orientation 输出屏幕方向指针（可为NULL）
 * @retval touch_result_t 获取结果
 */
touch_result_t Touch_GetScreenInfo(uint16_t *width, uint16_t *height, screen_orientation_t *orientation)
{
    /* 检查初始化状态 */
    if (!g_gt1158_config.is_initialized)
    {
        return TOUCH_RESULT_NOT_READY;
    }

    /* 返回当前配置信息 */
    if (width != NULL)
    {
        *width = g_gt1158_config.screen_width;
    }

    if (height != NULL)
    {
        *height = g_gt1158_config.screen_height;
    }

    if (orientation != NULL)
    {
        *orientation = g_gt1158_config.orientation;
    }

    return TOUCH_RESULT_OK;
}

/**
 * @brief  验证坐标是否在当前屏幕范围内
 * @note   公共函数，用于验证坐标的有效性，支持横竖屏动态切换
 * @param  x X坐标
 * @param  y Y坐标
 * @retval uint8_t 1-坐标有效，0-坐标无效
 */
uint8_t Touch_IsCoordinateInRange(uint16_t x, uint16_t y)
{
    /* 检查初始化状态 */
    if (!g_gt1158_config.is_initialized)
    {
        return 0;
    }

    /* 使用内部坐标验证函数 */
    return Touch_IsValidCoordinate(x, y);
}

/**
 * @brief  将坐标限制在当前屏幕范围内
 * @note   公共函数，确保坐标不超出屏幕边界，支持横竖屏动态切换
 * @param  x X坐标指针（输入输出参数）
 * @param  y Y坐标指针（输入输出参数）
 * @retval touch_result_t 处理结果
 */
touch_result_t Touch_ClampCoordinates(uint16_t *x, uint16_t *y)
{
    /* 参数检查 */
    if (x == NULL || y == NULL)
    {
        return TOUCH_RESULT_INVALID_PARAM;
    }

    /* 检查初始化状态 */
    if (!g_gt1158_config.is_initialized)
    {
        return TOUCH_RESULT_NOT_READY;
    }

    /* 获取当前屏幕尺寸 */
    uint16_t current_width = g_gt1158_config.screen_width;
    uint16_t current_height = g_gt1158_config.screen_height;

    /* 限制坐标范围 */
    if (*x >= current_width)
        *x = current_width - 1;
    if (*y >= current_height)
        *y = current_height - 1;

    return TOUCH_RESULT_OK;
}

/* ----------------------------------------------------------------------------------------
 * 触摸数据处理函数实现
 * ---------------------------------------------------------------------------------------- */

/**
 * @brief  扫描GT1158原始触摸数据
 * @note   从GT1158芯片读取原始触摸数据并进行基本处理
 * @param  None
 * @retval touch_result_t 扫描结果
 */
static touch_result_t Touch_ScanRawData(void)
{
    uint8_t status_cmd[2] = {GT1158_REG_STATUS >> 8, GT1158_REG_STATUS & 0xFF};
    uint8_t point_data[2 + 1 + GT1158_MAX_TOUCH_POINTS * GT1158_POINT_DATA_SIZE];
    uint8_t touch_count = 0;
    uint8_t status_reg = 0;
    static uint16_t previous_touch_count = 0;
    uint8_t *coordinate_data = NULL;
    uint16_t raw_x = 0, raw_y = 0, pressure = 0;
    uint8_t track_id = 0;
    int32_t i2c_result = 0;
    uint8_t i = 0;

    /* 检查初始化状态 */
    if (!g_gt1158_config.is_initialized)
    {
        return TOUCH_RESULT_NOT_READY;
    }

    /* 第一步：发送状态寄存器地址 */
    memcpy(point_data, status_cmd, 2);
    i2c_result = Touch_I2C_Write(GT1158_I2C_ADDR, point_data, 2);
    if (i2c_result < 0)
    {
        return TOUCH_RESULT_I2C_ERROR;
    }

    /* 第二步：读取状态寄存器和第一个触摸点数据 */
    i2c_result = Touch_I2C_Read(GT1158_I2C_ADDR, &point_data[2],
                               1 + GT1158_MAX_TOUCH_POINTS * GT1158_POINT_DATA_SIZE);
    if (i2c_result < 0)
    {
        return TOUCH_RESULT_I2C_ERROR;
    }

    /* 第三步：解析状态寄存器 */
    status_reg = point_data[2];

    /* 检查是否有触摸数据 */
    if (status_reg == 0x00)
    {
        return TOUCH_RESULT_NO_TOUCH;
    }

    /* 检查数据就绪标志 */
    if ((status_reg & GT1158_STATUS_READY_FLAG) == 0)
    {
        return TOUCH_RESULT_NO_TOUCH;
    }

    /* 第四步：获取触摸点数量 */
    touch_count = status_reg & GT1158_POINT_COUNT_MASK;
    if (touch_count > GT1158_MAX_TOUCH_POINTS)
    {
        return TOUCH_RESULT_ERROR;
    }

    /* 第五步：如果有多个触摸点，读取剩余触摸点数据 */
    if (touch_count > 1)
    {
        uint8_t additional_data_cmd[2] = {(GT1158_REG_STATUS + 10) >> 8, (GT1158_REG_STATUS + 10) & 0xFF};
        uint8_t additional_data[8 * GT1158_MAX_TOUCH_POINTS];

        /* 发送剩余数据寄存器地址 */
        i2c_result = Touch_I2C_Write(GT1158_I2C_ADDR, additional_data_cmd, 2);
        if (i2c_result < 0)
        {
            return TOUCH_RESULT_I2C_ERROR;
        }

        /* 读取剩余触摸点数据 */
        i2c_result = Touch_I2C_Read(GT1158_I2C_ADDR, additional_data, (touch_count - 1) * 8);
        if (i2c_result < 0)
        {
            return TOUCH_RESULT_I2C_ERROR;
        }

        /* 将剩余数据复制到主数据缓冲区 */
        memcpy(&point_data[4 + 8], additional_data, (touch_count - 1) * 8);
    }

    /* 第六步：更新全局触摸数据结构 */
    g_gt1158_touch_data.point_count = touch_count;
    g_gt1158_touch_data.status_reg = status_reg;
    g_gt1158_touch_data.timestamp_ms = HAL_GetTick();

    /* 第七步：解析各个触摸点的坐标数据 */
    if (touch_count > 0)
    {
        for (i = 0; i < touch_count; i++)
        {
            /* 获取当前触摸点的数据指针 */
            coordinate_data = &point_data[i * 8 + 3];

            /* 解析触摸点数据 */
            track_id = coordinate_data[0] & 0x0F;
            raw_x = coordinate_data[1] | (coordinate_data[2] << 8);
            raw_y = coordinate_data[3] | (coordinate_data[4] << 8);
            pressure = coordinate_data[5] | (coordinate_data[6] << 8);

            /* 坐标转换和边界检查 */
            Touch_TransformCoordinates(&raw_x, &raw_y);

            /* 边界限制 - 使用当前配置的屏幕尺寸 */
            uint16_t current_width = g_gt1158_config.screen_width;
            uint16_t current_height = g_gt1158_config.screen_height;

            if (raw_x >= current_width)
                raw_x = current_width - 1;
            if (raw_y >= current_height)
                raw_y = current_height - 1;

            /* 更新触摸点数据 */
            if (i < GT1158_MAX_TOUCH_POINTS)
            {
                g_gt1158_touch_data.points[i].x = raw_x;
                g_gt1158_touch_data.points[i].y = raw_y;
                g_gt1158_touch_data.points[i].pressure = pressure;
                g_gt1158_touch_data.points[i].track_id = track_id;
                g_gt1158_touch_data.points[i].is_valid = 1;
            }
        }
    }
    else if (previous_touch_count > 0)
    {
        /* 触摸释放，清除所有触摸点 */
        for (i = 0; i < GT1158_MAX_TOUCH_POINTS; i++)
        {
            g_gt1158_touch_data.points[i].is_valid = 0;
        }
    }

    /* 更新前一次触摸点数量 */
    previous_touch_count = touch_count;

    /* 第八步：清除状态寄存器（告知芯片数据已读取） */
    uint8_t clear_status_cmd[3] = {GT1158_REG_STATUS >> 8, GT1158_REG_STATUS & 0xFF, 0x00};
    Touch_I2C_Write(GT1158_I2C_ADDR, clear_status_cmd, 3);

    return TOUCH_RESULT_OK;
}

/* ----------------------------------------------------------------------------------------
 * 触摸数据读取函数实现
 * ---------------------------------------------------------------------------------------- */

/**
 * @brief  读取多点触摸数据
 * @note   读取所有有效触摸点的完整数据
 * @param  touch_data 触摸数据结构指针（输出参数）
 * @retval touch_result_t 读取结果
 */
touch_result_t Touch_ReadMultiPointData(gt1158_touch_data_t *touch_data)
{
    touch_result_t scan_result;

    /* 参数有效性检查 */
    if (touch_data == NULL)
    {
        return TOUCH_RESULT_INVALID_PARAM;
    }

    /* 扫描原始触摸数据 */
    scan_result = Touch_ScanRawData();
    if (scan_result == TOUCH_RESULT_OK)
    {
        /* 复制触摸数据到用户缓冲区 */
        memcpy(touch_data, &g_gt1158_touch_data, sizeof(gt1158_touch_data_t));
        return TOUCH_RESULT_OK;
    }
    else if (scan_result == TOUCH_RESULT_NO_TOUCH)
    {
        /* 无触摸数据，清空用户缓冲区 */
        memset(touch_data, 0, sizeof(gt1158_touch_data_t));
        return TOUCH_RESULT_NO_TOUCH;
    }

    return scan_result;
}

/* ----------------------------------------------------------------------------------------
 * 3. 触摸数据读取函数实现
 * ---------------------------------------------------------------------------------------- */

/**
 * @brief  获取单点触摸坐标
 * @note   获取第一个有效触摸点的坐标信息
 * @param  x X坐标指针（输出参数）
 * @param  y Y坐标指针（输出参数）
 * @retval touch_result_t 获取结果
 */
touch_result_t Touch_GetSinglePoint(uint16_t *x, uint16_t *y)
{
    touch_result_t scan_result;

    /* 参数有效性检查 */
    if (x == NULL || y == NULL)
    {
        return TOUCH_RESULT_INVALID_PARAM;
    }

    /* 扫描原始触摸数据 */
    scan_result = Touch_ScanRawData();
    if (scan_result == TOUCH_RESULT_OK)
    {
        /* 检查是否有有效的触摸点 */
        if (g_gt1158_touch_data.point_count > 0 && g_gt1158_touch_data.points[0].is_valid)
        {
            *x = g_gt1158_touch_data.points[0].x;
            *y = g_gt1158_touch_data.points[0].y;
            return TOUCH_RESULT_OK;
        }
        else
        {
            return TOUCH_RESULT_NO_TOUCH;
        }
    }

    return scan_result;
}

/* ----------------------------------------------------------------------------------------
 * 私有工具函数实现
 * ---------------------------------------------------------------------------------------- */

/**
 * @brief  重置手势检测状态
 * @param  None
 * @retval None
 */
static void Touch_ResetGestureState(void)
{
    memset(&g_touch_state, 0, sizeof(touch_state_manager_t));
    memset(&g_gesture_event, 0, sizeof(touch_gesture_event_t));
    g_touch_state.current_state = TOUCH_STATE_IDLE;
}

/**
 * @brief  坐标转换函数（自定义坐标系统）
 * @note   根据屏幕方向转换坐标，匹配LCD的坐标系统
 * @param  x X坐标指针
 * @param  y Y坐标指针
 * @retval None
 */
static void Touch_TransformCoordinates(uint16_t *x, uint16_t *y)
{
    uint16_t temp_x = *x;
    uint16_t temp_y = *y;

    switch (g_gt1158_config.orientation)
    {
        case SCREEN_ORIENTATION_PORTRAIT:
            /*
             * 横屏模式（LCD方向1，触摸方向0）：
             * - 触摸芯片原始坐标与LCD坐标直接匹配
             * - 无需转换
             */
            break;

        case SCREEN_ORIENTATION_LANDSCAPE:
            /*
             * 竖屏模式（LCD方向0，触摸方向1）：
             * - LCD竖屏(0,0)：横屏状态下的左下角
             * - LCD竖屏X增加：横屏左下角→左上角（向上）
             * - LCD竖屏Y增加：横屏左下角→右下角（向右）
             *
             * 触摸芯片原始坐标系统（横屏基准）：
             * - 原始(0,0)：左上角
             * - 原始X增加：向右
             * - 原始Y增加：向下
             *
             * 转换公式推导：
             * 原始坐标 → 竖屏坐标的映射：
             * - 横屏左下角(0,479) → 竖屏(0,0)
             * - 横屏左上角(0,0) → 竖屏(479,0)
             * - 横屏右下角(799,479) → 竖屏(0,799)
             * - 横屏右上角(799,0) → 竖屏(479,799)
             *
             * 转换公式：
             * - 竖屏X = 原始Y的反向映射：479 - 原始Y
             * - 竖屏Y = 原始X（向右为正）
             */
            *x = (LCD_PANEL_HEIGHT - 1) - temp_y;  /* 479 - temp_y */
            *y = temp_x;
            break;

        case SCREEN_ORIENTATION_PORTRAIT_FLIP:
            /* 竖屏翻转，180度旋转 */
            *x = g_gt1158_config.screen_width - temp_x;
            *y = g_gt1158_config.screen_height - temp_y;
            break;

        case SCREEN_ORIENTATION_LANDSCAPE_FLIP:
            /* 横屏翻转，270度旋转 */
            *x = g_gt1158_config.screen_height - temp_y;
            *y = temp_x;
            break;

        default:
            /* 默认不转换 */
            break;
    }
}

/**
 * @brief  检查坐标是否有效（支持动态屏幕尺寸）
 * @note   自动适配当前屏幕配置，支持横竖屏切换
 * @param  x X坐标
 * @param  y Y坐标
 * @retval uint8_t 1-有效，0-无效
 */
static uint8_t Touch_IsValidCoordinate(uint16_t x, uint16_t y)
{
    /* 获取当前有效的屏幕尺寸 */
    uint16_t current_width = g_gt1158_config.screen_width;
    uint16_t current_height = g_gt1158_config.screen_height;

    /* 边界检查 - 使用当前配置的屏幕尺寸 */
    if (x >= current_width || y >= current_height)
    {
        return 0;
    }

    /* 防误触区域检查（屏幕边缘区域） - 动态适配屏幕尺寸 */
    const uint16_t edge_threshold = TOUCH_EDGE_THRESHOLD_PX;
    if (x < edge_threshold || y < edge_threshold ||
        x > (current_width - edge_threshold) ||
        y > (current_height - edge_threshold))
    {
        /* 边缘区域需要更严格的检查 */
        static uint32_t last_edge_touch_time = 0;
        uint32_t current_time = HAL_GetTick();

        if (current_time - last_edge_touch_time < TOUCH_DEBOUNCE_TIME_MS)
        {
            return 0;  /* 防抖时间内的边缘触摸认为是误触 */
        }

        last_edge_touch_time = current_time;
    }

    return 1;  /* 有效触摸 */
}

/* ----------------------------------------------------------------------------------------
 * 5. 工具函数实现
 * ---------------------------------------------------------------------------------------- */

/**
 * @brief  计算两点间距离
 * @note   使用曼哈顿距离算法，避免浮点运算
 * @param  x1 第一个点X坐标
 * @param  y1 第一个点Y坐标
 * @param  x2 第二个点X坐标
 * @param  y2 第二个点Y坐标
 * @retval uint16_t 距离值
 */
static uint16_t Touch_CalculateDistance(uint16_t x1, uint16_t y1, uint16_t x2, uint16_t y2)
{
    int32_t dx = x2 - x1;
    int32_t dy = y2 - y1;

    /* 简化的距离计算（避免浮点运算） */
    if (dx < 0) dx = -dx;
    if (dy < 0) dy = -dy;

    return (uint16_t)(dx + dy);  /* 曼哈顿距离 */
}

/**
 * @brief  获取当前有效屏幕尺寸
 * @note   返回当前触摸配置的屏幕尺寸，支持横竖屏动态切换
 * @param  width 输出屏幕宽度指针
 * @param  height 输出屏幕高度指针
 * @retval None
 */
static void Touch_GetCurrentScreenSize(uint16_t *width, uint16_t *height)
{
    if (width != NULL)
    {
        *width = g_gt1158_config.screen_width;
    }

    if (height != NULL)
    {
        *height = g_gt1158_config.screen_height;
    }
}

/* ----------------------------------------------------------------------------------------
 * 触摸事件处理函数实现
 * ---------------------------------------------------------------------------------------- */

/* 注意：Touch_ProcessEvents 函数已被删除，因为 Touch_WaitForInput 函数已经提供了相同的功能 */

/* ----------------------------------------------------------------------------------------
 * 中断处理函数实现
 * ---------------------------------------------------------------------------------------- */

/**
 * @brief  使能触摸中断
 * @note   启用触摸芯片的GPIO中断功能，配置PD13外部中断
 * @param  None
 * @retval None
 */
void Touch_EnableInterrupt(void)
{
    GPIO_InitTypeDef gpio_init_struct = {0};

    /* 确保GPIO时钟已使能 */
    __HAL_RCC_GPIOD_CLK_ENABLE();

    /* 配置PD13为外部中断模式 */
    gpio_init_struct.Pin = GT1158_INT_PIN;          // PD13
    gpio_init_struct.Mode = GPIO_MODE_IT_FALLING;   // 下降沿触发
    gpio_init_struct.Pull = GPIO_PULLUP;            // 上拉
    gpio_init_struct.Speed = GPIO_SPEED_FREQ_HIGH;
    HAL_GPIO_Init(GT1158_INT_PORT, &gpio_init_struct);

    /* 配置NVIC中断优先级并使能 */
    HAL_NVIC_SetPriority(EXTI15_10_IRQn, 1, 0);    // 优先级1
    HAL_NVIC_EnableIRQ(EXTI15_10_IRQn);

    /* 清除可能存在的挂起中断 */
    __HAL_GPIO_EXTI_CLEAR_IT(GT1158_INT_PIN);

    /* 清除中断标志 */
    s_touch_interrupt_flag = 0;
    g_touch_event_flag = 0;
}

/**
 * @brief  禁用触摸中断
 * @note   关闭触摸芯片的GPIO中断功能
 * @param  None
 * @retval None
 */
void Touch_DisableInterrupt(void)
{
    /* 禁用NVIC中断 */
    HAL_NVIC_DisableIRQ(EXTI15_10_IRQn);

    /* 将PD13重新配置为普通输入模式 */
    GPIO_InitTypeDef gpio_init_struct = {0};
    gpio_init_struct.Pin = GT1158_INT_PIN;
    gpio_init_struct.Mode = GPIO_MODE_INPUT;
    gpio_init_struct.Pull = GPIO_PULLUP;
    gpio_init_struct.Speed = GPIO_SPEED_FREQ_HIGH;
    HAL_GPIO_Init(GT1158_INT_PORT, &gpio_init_struct);

    /* 清除中断标志 */
    s_touch_interrupt_flag = 0;
    g_touch_event_flag = 0;
}

/**
 * @brief  触摸中断处理函数
 * @note   这是GPIO中断回调的核心函数，可以在以下场景中调用：
 *         1. 在HAL_GPIO_EXTI_Callback()中调用（推荐）
 *         2. 在中断服务程序中直接调用
 *         3. 在主循环中检查标志并处理事件
 * @param  None
 * @retval None
 */
void Touch_InterruptHandler(void)
{
    /* 设置触摸中断标志，在主循环中处理 */
    s_touch_interrupt_flag = 1;
    g_touch_event_flag = 1;

    /* 注意：Touch_ProcessEvents 函数已被删除，请在主循环中使用 Touch_WaitForInput 函数 */
}

/* ----------------------------------------------------------------------------------------
 * 手势检测函数实现
 * ---------------------------------------------------------------------------------------- */

/**
 * @brief  启用或禁用手势检测功能
 * @note   控制手势识别功能的开关
 * @param  enable 使能标志（1-启用，0-禁用）
 * @retval touch_result_t 设置结果
 */
touch_result_t Touch_SetGestureDetection(uint8_t enable)
{
    g_gt1158_config.gesture_detection_enable = enable ? 1 : 0;

    if (!enable)
    {
        /* 禁用手势检测时重置状态 */
        Touch_ResetGestureState();
    }

    return TOUCH_RESULT_OK;
}

/**
 * @brief  获取最新的手势事件
 * @note   获取并清除最新的手势事件数据
 * @param  gesture_event 手势事件结构指针（输出参数）
 * @retval touch_result_t 获取结果
 */
touch_result_t Touch_GetGestureEvent(touch_gesture_event_t *gesture_event)
{
    if (gesture_event == NULL)
    {
        return TOUCH_RESULT_INVALID_PARAM;
    }

    if (g_gesture_event.gesture != TOUCH_GESTURE_NONE)
    {
        memcpy(gesture_event, &g_gesture_event, sizeof(touch_gesture_event_t));

        /* 清除已读取的事件 */
        memset(&g_gesture_event, 0, sizeof(touch_gesture_event_t));

        return TOUCH_RESULT_OK;
    }

    return TOUCH_RESULT_NO_TOUCH;
}

/* ----------------------------------------------------------------------------------------
 * 4. 手势检测函数实现
 * ---------------------------------------------------------------------------------------- */

/**
 * @brief  检测触摸手势（优化版本）
 * @note   基于当前触摸状态检测手势类型，增强防误触和准确性
 * @param  None
 * @retval touch_gesture_type_t 检测到的手势类型
 */
static touch_gesture_type_t Touch_DetectGesture(void)
{
    if (!g_gt1158_config.gesture_detection_enable)
    {
        return TOUCH_GESTURE_NONE;
    }

    uint32_t current_time = HAL_GetTick();
    uint16_t x, y;

    /* 获取当前触摸状态 */
    touch_result_t touch_result = Touch_GetSinglePoint(&x, &y);

    switch (g_touch_state.current_state)
    {
        case TOUCH_STATE_IDLE:
            if (touch_result == TOUCH_RESULT_OK && Touch_IsValidCoordinate(x, y))
            {
                /* 防误触检查：边缘区域检测 - 使用动态屏幕尺寸 */
                uint16_t current_width = g_gt1158_config.screen_width;
                uint16_t current_height = g_gt1158_config.screen_height;

                if (x < TOUCH_EDGE_THRESHOLD_PX || x > (current_width - TOUCH_EDGE_THRESHOLD_PX) ||
                    y < TOUCH_EDGE_THRESHOLD_PX || y > (current_height - TOUCH_EDGE_THRESHOLD_PX))
                {
                    /* 边缘区域，可能是误触，忽略 */
                    return TOUCH_GESTURE_NONE;
                }

                /* 触摸按下 */
                g_touch_state.current_state = TOUCH_STATE_PRESSED;
                g_touch_state.press_x = x;
                g_touch_state.press_y = y;
                g_touch_state.current_x = x;
                g_touch_state.current_y = y;
                g_touch_state.press_timestamp_ms = current_time;
                g_touch_state.is_moving = 0;
                g_touch_state.long_press_triggered = 0;

                return TOUCH_GESTURE_PRESS;
            }
            break;

        case TOUCH_STATE_PRESSED:
            if (touch_result == TOUCH_RESULT_OK)
            {
                /* 更新当前坐标 */
                g_touch_state.current_x = x;
                g_touch_state.current_y = y;

                /* 防抖处理：检查按下时间是否足够长 */
                uint32_t press_duration = current_time - g_touch_state.press_timestamp_ms;
                if (press_duration < TOUCH_DEBOUNCE_TIME_MS)
                {
                    /* 按下时间太短，可能是抖动，继续等待 */
                    return TOUCH_GESTURE_NONE;
                }

                /* 检查是否移动 */
                uint16_t move_distance = Touch_CalculateDistance(g_touch_state.press_x, g_touch_state.press_y, x, y);

                if (move_distance > TOUCH_MOVE_THRESHOLD_PX)
                {
                    g_touch_state.current_state = TOUCH_STATE_MOVING;
                    g_touch_state.is_moving = 1;
                    return TOUCH_GESTURE_MOVE;
                }

                /* 检查长按 */
                if (press_duration >= TOUCH_LONG_PRESS_TIME_MS && !g_touch_state.long_press_triggered)
                {
                    g_touch_state.long_press_triggered = 1;
                    /* 长按触发后，等待用户释放触摸，不改变状态 */

                    /* 填充手势事件 */
                    g_gesture_event.gesture = TOUCH_GESTURE_LONG_PRESS;
                    g_gesture_event.x = x;
                    g_gesture_event.y = y;
                    g_gesture_event.start_x = g_touch_state.press_x;
                    g_gesture_event.start_y = g_touch_state.press_y;
                    g_gesture_event.duration_ms = press_duration;
                    g_gesture_event.timestamp_ms = current_time;

                    return TOUCH_GESTURE_LONG_PRESS;
                }

                /* 长按已触发，等待释放，不再重复检测 */
                if (g_touch_state.long_press_triggered)
                {
                    return TOUCH_GESTURE_NONE;
                }
            }
            else
            {
                /* 触摸释放 */
                g_touch_state.current_state = TOUCH_STATE_RELEASED;
                g_touch_state.release_timestamp_ms = current_time;

                uint32_t press_duration = current_time - g_touch_state.press_timestamp_ms;

                /* 防误触：检查按下时间是否太短 */
                if (press_duration < TOUCH_DEBOUNCE_TIME_MS)
                {
                    /* 按下时间太短，认为是误触，直接回到空闲状态 */
                    g_touch_state.current_state = TOUCH_STATE_IDLE;
                    return TOUCH_GESTURE_NONE;
                }

                if (!g_touch_state.is_moving && !g_touch_state.long_press_triggered)
                {
                    /* 单击检测 - 未移动且未触发长按 */
                    /* 修复：只要未达到长按时间且未移动，就认为是单击 */
                    if (press_duration < TOUCH_LONG_PRESS_TIME_MS)
                    {
                        g_touch_state.current_state = TOUCH_STATE_IDLE;

                        /* 填充单击手势事件 */
                        g_gesture_event.gesture = TOUCH_GESTURE_CLICK;
                        g_gesture_event.x = g_touch_state.press_x;
                        g_gesture_event.y = g_touch_state.press_y;
                        g_gesture_event.duration_ms = press_duration;
                        g_gesture_event.timestamp_ms = current_time;

                        return TOUCH_GESTURE_CLICK;
                    }
                }
                else if (g_touch_state.long_press_triggered)
                {
                    /* 长按后释放，直接回到空闲状态，不产生其他手势 */
                    g_touch_state.current_state = TOUCH_STATE_IDLE;
                    return TOUCH_GESTURE_RELEASE;
                }
                else if (g_touch_state.is_moving)
                {
                    /* 检测滑动手势 */
                    touch_gesture_type_t swipe_gesture = Touch_DetectSwipeGesture();
                    if (swipe_gesture != TOUCH_GESTURE_NONE)
                    {
                        g_touch_state.current_state = TOUCH_STATE_IDLE;
                        return swipe_gesture;
                    }
                }

                /* 其他情况返回释放事件 */
                g_touch_state.current_state = TOUCH_STATE_IDLE;
                return TOUCH_GESTURE_RELEASE;
            }
            break;

        case TOUCH_STATE_MOVING:
            if (touch_result == TOUCH_RESULT_OK)
            {
                /* 更新当前坐标 */
                g_touch_state.current_x = x;
                g_touch_state.current_y = y;
                return TOUCH_GESTURE_MOVE;
            }
            else
            {
                /* 移动结束，检查是否为滑动手势 */
                g_touch_state.current_state = TOUCH_STATE_RELEASED;
                g_touch_state.release_timestamp_ms = current_time;

                return Touch_DetectSwipeGesture();
            }
            break;

        case TOUCH_STATE_RELEASED:
            /* 检查是否有新的触摸 */
            if (touch_result == TOUCH_RESULT_OK && Touch_IsValidCoordinate(x, y))
            {
                /* 有新的触摸，开始新的触摸检测 */
                g_touch_state.current_state = TOUCH_STATE_PRESSED;
                g_touch_state.press_x = x;
                g_touch_state.press_y = y;
                g_touch_state.current_x = x;
                g_touch_state.current_y = y;
                g_touch_state.press_timestamp_ms = current_time;
                g_touch_state.is_moving = 0;
                g_touch_state.long_press_triggered = 0;

                return TOUCH_GESTURE_PRESS;
            }
            else
            {
                /* 无新触摸，转换回IDLE状态 */
                g_touch_state.current_state = TOUCH_STATE_IDLE;
            }
            break;
    }

    return TOUCH_GESTURE_NONE;
}

/**
 * @brief  检测滑动手势（优化版本）
 * @note   基于起始和结束坐标检测滑动方向，增强防误触和准确性
 * @param  None
 * @retval touch_gesture_type_t 滑动手势类型
 */
static touch_gesture_type_t Touch_DetectSwipeGesture(void)
{
    int16_t dx = g_touch_state.current_x - g_touch_state.press_x;
    int16_t dy = g_touch_state.current_y - g_touch_state.press_y;

    uint16_t distance = Touch_CalculateDistance(g_touch_state.press_x, g_touch_state.press_y,
                                               g_touch_state.current_x, g_touch_state.current_y);

    /* 检查是否达到滑动阈值 */
    if (distance < TOUCH_SWIPE_THRESHOLD_PX)
    {
        return TOUCH_GESTURE_NONE;
    }

    /* 计算滑动速度 */
    uint32_t duration = g_touch_state.release_timestamp_ms - g_touch_state.press_timestamp_ms;
    if (duration == 0) duration = 1;  /* 避免除零 */

    uint32_t speed = (distance * 1000) / duration;  /* 像素/秒 */

    if (speed < TOUCH_SWIPE_MIN_SPEED)
    {
        return TOUCH_GESTURE_NONE;
    }

    /* 防误触：检查滑动时间是否合理 */
    if (duration > 2000)  /* 滑动时间超过2秒，可能不是有意的滑动 */
    {
        return TOUCH_GESTURE_NONE;
    }

    /* 判断滑动方向，增加方向判断的准确性 */
    touch_gesture_type_t swipe_gesture = TOUCH_GESTURE_NONE;

    /* 计算主要方向的比例，防止斜向滑动被误判 */
    uint16_t abs_dx = abs(dx);
    uint16_t abs_dy = abs(dy);

    /* 要求主要方向至少是次要方向的1.5倍 */
    if (abs_dx > abs_dy && abs_dx > (abs_dy * 3 / 2))
    {
        /* 水平滑动 */
        if (dx > 0)
            swipe_gesture = TOUCH_GESTURE_SWIPE_RIGHT;
        else
            swipe_gesture = TOUCH_GESTURE_SWIPE_LEFT;
    }
    else if (abs_dy > abs_dx && abs_dy > (abs_dx * 3 / 2))
    {
        /* 垂直滑动 */
        if (dy > 0)
            swipe_gesture = TOUCH_GESTURE_SWIPE_DOWN;
        else
            swipe_gesture = TOUCH_GESTURE_SWIPE_UP;
    }
    else
    {
        /* 斜向滑动，不识别为有效滑动 */
        return TOUCH_GESTURE_NONE;
    }

    /* 填充手势事件 */
    g_gesture_event.gesture = swipe_gesture;
    g_gesture_event.start_x = g_touch_state.press_x;
    g_gesture_event.start_y = g_touch_state.press_y;
    g_gesture_event.end_x = g_touch_state.current_x;
    g_gesture_event.end_y = g_touch_state.current_y;
    g_gesture_event.duration_ms = duration;
    g_gesture_event.timestamp_ms = g_touch_state.release_timestamp_ms;

    return swipe_gesture;
}

/* ========================================================================================
 * 应用层回调函数的默认实现（用户可以重新实现这些函数）
 * ======================================================================================== */

/* ----------------------------------------------------------------------------------------
 * 6. 事件回调函数实现（弱定义，用户可重新实现）
 * ---------------------------------------------------------------------------------------- */

/**
 * @brief  触摸按下事件回调函数（支持动态屏幕尺寸）
 * @note   当检测到触摸按下时调用，用户可重新实现此函数
 *         自动适配当前屏幕配置，支持横竖屏切换
 * @param  x X坐标值
 * @param  y Y坐标值
 * @retval None
 */
__weak void Touch_OnPress(uint16_t x, uint16_t y)
{

}

/**
 * @brief  触摸释放事件回调函数（支持动态屏幕尺寸）
 * @note   当检测到触摸释放时调用，用户可重新实现此函数
 *         自动适配当前屏幕配置，支持横竖屏切换
 * @param  x X坐标值
 * @param  y Y坐标值
 * @retval None
 */
__weak void Touch_OnRelease(uint16_t x, uint16_t y)
{

}

/**
 * @brief  触摸移动轨迹绘制回调函数（支持动态屏幕尺寸）
 * @note   当检测到触摸移动时调用，用于绘制轨迹
 *         自动适配当前屏幕配置，支持横竖屏切换
 * @param  prev_x 前一个X坐标
 * @param  prev_y 前一个Y坐标
 * @param  curr_x 当前X坐标
 * @param  curr_y 当前Y坐标
 * @param  brush_params 画笔参数（可为NULL）
 * @retval None
 */
__weak void Touch_OnDrawTrail(int16_t prev_x, int16_t prev_y, int16_t curr_x, int16_t curr_y, void *brush_params)
{
 
}

/* ----------------------------------------------------------------------------------------
 * 手势事件回调函数默认实现（弱定义，用户可重新实现）
 * ---------------------------------------------------------------------------------------- */

/**
 * @brief  单击手势回调函数
 * @param  x 单击位置X坐标
 * @param  y 单击位置Y坐标
 * @retval None
 */
__weak void Touch_OnClick(uint16_t x, uint16_t y)
{
 
}

/**
 * @brief  长按手势回调函数
 * @param  x 长按位置X坐标
 * @param  y 长按位置Y坐标
 * @param  duration_ms 长按持续时间（毫秒）
 * @retval None
 */
__weak void Touch_OnLongPress(uint16_t x, uint16_t y, uint32_t duration_ms)
{

}

/**
 * @brief  滑动手势回调函数
 * @param  gesture_type 滑动手势类型
 * @param  start_x 滑动起始X坐标
 * @param  start_y 滑动起始Y坐标
 * @param  end_x 滑动结束X坐标
 * @param  end_y 滑动结束Y坐标
 * @retval None
 */
__weak void Touch_OnSwipe(touch_gesture_type_t gesture_type, uint16_t start_x, uint16_t start_y, uint16_t end_x, uint16_t end_y)
{
  
}

/**
 * @brief  移动手势回调函数
 * @param  x 当前X坐标
 * @param  y 当前Y坐标
 * @param  delta_x X方向移动距离
 * @param  delta_y Y方向移动距离
 * @retval None
 */
__weak void Touch_OnMove(uint16_t x, uint16_t y, int16_t delta_x, int16_t delta_y)
{
 
}

/* ========================================================================================
 * GPIO中断回调函数实现
 * ======================================================================================== */

/**
 * @brief  GPIO外部中断回调函数
 * @note   当PD13引脚检测到下降沿时，HAL库会自动调用此函数
 *         这是HAL库的标准回调函数，用户可以重新实现
 * @param  GPIO_Pin 触发中断的GPIO引脚
 * @retval None
 */
void HAL_GPIO_EXTI_Callback(uint16_t GPIO_Pin)
{
    /* 检查是否为触摸中断引脚 */
    if (GPIO_Pin == GT1158_INT_PIN)  // PD13
    {
        /* 直接调用触摸中断处理函数 */
        Touch_InterruptHandler();
    }
}


/**
 * @brief  阻塞式等待触摸输入（优化版本）
 * @note   基于中断驱动的手势检测，支持5种手势：单击、上滑、下滑、左滑、右滑
 *         不检测长按手势，避免长按状态的重复检测问题
 * @param  timeout 超时时间(毫秒)，TOUCH_WAIT_ALWAYS表示一直等待
 * @retval touch_gesture_type_t 检测到的手势类型（单击或滑动），超时返回TOUCH_GESTURE_NONE
 */
touch_gesture_type_t Touch_WaitForInput(uint32_t timeout)
{
    uint32_t tickstart = HAL_GetTick();
    touch_gesture_type_t detected_gesture = TOUCH_GESTURE_NONE;

    /* 确保手势检测功能已启用 */
    if (!g_gt1158_config.gesture_detection_enable)
    {
        Touch_SetGestureDetection(1);
    }

    /* 重置手势检测状态 */
    Touch_ResetGestureState();

    /* 清除之前的事件标志 */
    g_touch_event_flag = 0;
    s_touch_interrupt_flag = 0;

    /* 主等待循环 - 参考ScanPressedKey的设计 */
    while (1)
    {
        /* 检查是否有触摸中断事件 */
        if (g_touch_event_flag || s_touch_interrupt_flag)
        {
            /* 清除事件标志 */
            g_touch_event_flag = 0;
            s_touch_interrupt_flag = 0;

            /* 调用现有的手势检测函数 */
            detected_gesture = Touch_DetectGesture();

            /* 检查是否检测到完整的手势（不包括长按） */
            if (detected_gesture == TOUCH_GESTURE_CLICK ||
                detected_gesture == TOUCH_GESTURE_SWIPE_LEFT ||
                detected_gesture == TOUCH_GESTURE_SWIPE_RIGHT ||
                detected_gesture == TOUCH_GESTURE_SWIPE_UP ||
                detected_gesture == TOUCH_GESTURE_SWIPE_DOWN)
            {
                /* 检测到目标手势，返回结果 */
                return detected_gesture;
            }

            /* 如果是中间状态（PRESS, RELEASE, MOVE），继续等待 */
        }

        /* 超时判断 */
        if (timeout != TOUCH_WAIT_ALWAYS)
        {
            if ((HAL_GetTick() - tickstart) > timeout)
            {
                return TOUCH_GESTURE_NONE;
            }
        }

        /* 短暂延时，避免CPU占用过高，但要保证长按检测的及时性 */
        HAL_Delay(10);  /* 减少延时以提高长按检测的响应性 */
    }
}







