#ifndef INC_TOUCH_H_
#define INC_TOUCH_H_

#include "main.h"
#include "i2c.h"
#include "lcd.h"

/* ========================================================================================
 * GT1158/GT9xx 触摸芯片驱动头文件
 * ========================================================================================
 * @file    touch.h
 * @brief   GT1158/GT9xx 触摸芯片驱动头文件 - 支持横竖屏动态切换
 * <AUTHOR> Project Team
 * @version V4.0
 * @date    2025-01-27
 *
 * @note    功能特性：
 *          - 支持多点触摸（最多5个触摸点）
 *          - 支持手势识别（单击、长按、滑动等）
 *          - 支持横竖屏动态切换和坐标自动转换
 *          - 防误触和边缘检测
 *          - 使用I2C2接口进行通信
 *          - 硬件连接：RESET(PI8)、INT(PD13)
 *          - 简洁可靠的实现
 * ======================================================================================== */

/* ========================================================================================
 * 硬件配置定义
 * ======================================================================================== */

/* I2C接口句柄声明 */
extern I2C_HandleTypeDef hi2c2;

/* ========================================================================================
 * GT1158 芯片配置参数
 * ======================================================================================== */

/* I2C通信配置 */
#define GT1158_I2C_ADDR             0x28    ///< GT1158固定I2C设备地址
#define GT1158_I2C_RETRY_COUNT      5       ///< I2C通信重试次数
#define GT1158_I2C_TIMEOUT_MS       1000    ///< I2C通信超时时间(毫秒)

/* GPIO管脚配置 */
#define GT1158_RST_PORT             GPIOI   ///< 复位管脚端口
#define GT1158_RST_PIN              GPIO_PIN_8   ///< 复位管脚引脚
#define GT1158_INT_PORT             GPIOD   ///< 中断管脚端口
#define GT1158_INT_PIN              GPIO_PIN_13  ///< 中断管脚引脚

/* 寄存器地址定义 */
#define GT1158_REG_STATUS           0x814E  ///< 触摸状态寄存器地址
#define GT1158_REG_PRODUCT_ID       0x8140  ///< 产品ID寄存器地址
#define GT1158_REG_FIRMWARE_VER     0x8144  ///< 固件版本寄存器地址
#define GT1158_REG_CONFIG_DATA      0x8047  ///< 配置数据寄存器地址

/* 触摸参数配置 */
#define GT1158_MAX_TOUCH_POINTS     5       ///< 最大支持触摸点数量
#define GT1158_POINT_DATA_SIZE      8       ///< 单个触摸点数据字节数

/* 状态标志定义 */
#define GT1158_STATUS_READY_FLAG    0x80    ///< 触摸数据就绪标志位
#define GT1158_POINT_COUNT_MASK     0x0F    ///< 触摸点数量掩码

/* ========================================================================================
 * 触摸手势检测参数配置
 * ======================================================================================== */

/* 触摸参数配置 */
#define TOUCH_DEBOUNCE_TIME_MS      20      ///< 触摸防抖时间(毫秒)
#define TOUCH_CLICK_MAX_TIME_MS     300     ///< 单击最大持续时间(毫秒)
#define TOUCH_LONG_PRESS_TIME_MS    800     ///< 长按触发时间(毫秒)
#define TOUCH_MOVE_THRESHOLD_PX     10      ///< 移动检测阈值(像素)
#define TOUCH_SWIPE_THRESHOLD_PX    50      ///< 滑动检测阈值(像素)
#define TOUCH_SWIPE_MIN_SPEED       100     ///< 滑动最小速度(像素/秒)
#define TOUCH_EDGE_THRESHOLD_PX     5       ///< 边缘防误触阈值(像素)

/* 触摸等待常量 */
#define TOUCH_WAIT_ALWAYS           0       ///< 一直等待触摸输入

/* ========================================================================================
 * 数据类型定义
 * ======================================================================================== */

/**
 * @brief 触摸驱动函数返回值枚举
 * @note  统一的错误码定义，便于错误处理和调试
 */
typedef enum
{
    TOUCH_RESULT_OK = 0,                ///< 操作成功
    TOUCH_RESULT_ERROR = 1,             ///< 一般错误
    TOUCH_RESULT_I2C_ERROR = 2,         ///< I2C通信错误
    TOUCH_RESULT_TIMEOUT = 3,           ///< 操作超时错误
    TOUCH_RESULT_INVALID_PARAM = 4,     ///< 参数无效错误
    TOUCH_RESULT_NOT_READY = 5,         ///< 设备未就绪错误
    TOUCH_RESULT_NO_TOUCH = 6,          ///< 无触摸数据
    TOUCH_RESULT_BUSY = 7               ///< 设备忙碌状态
} touch_result_t;

/**
 * @brief 屏幕方向枚举
 * @note  支持四种屏幕方向，用于坐标转换
 */
typedef enum
{
    SCREEN_ORIENTATION_PORTRAIT = 0,        ///< 横屏模式（0度）
    SCREEN_ORIENTATION_LANDSCAPE = 1,       ///< 竖屏模式（90度）
    SCREEN_ORIENTATION_PORTRAIT_FLIP = 2,   ///< 竖屏翻转模式（180度）
    SCREEN_ORIENTATION_LANDSCAPE_FLIP = 3   ///< 横屏翻转模式（270度）
} screen_orientation_t;

/**
 * @brief 触摸点数据结构
 * @note  包含单个触摸点的完整信息
 */
typedef struct
{
    uint16_t x;                 ///< X坐标值（像素）
    uint16_t y;                 ///< Y坐标值（像素）
    uint16_t pressure;          ///< 触摸压力值
    uint8_t  track_id;          ///< 触摸点跟踪ID（用于多点触摸）
    uint8_t  is_valid;          ///< 触摸点数据有效标志
} touch_point_t;

/**
 * @brief GT1158触摸数据结构
 * @note  包含所有触摸点的数据信息
 */
typedef struct
{
    uint8_t         point_count;                        ///< 当前有效触摸点数量
    uint8_t         status_reg;                         ///< 触摸状态寄存器值
    touch_point_t   points[GT1158_MAX_TOUCH_POINTS];    ///< 触摸点数据数组
    uint32_t        timestamp_ms;                       ///< 数据时间戳（毫秒）
} gt1158_touch_data_t;

/**
 * @brief 触摸手势类型枚举
 * @note  定义所有支持的触摸手势类型
 */
typedef enum
{
    TOUCH_GESTURE_NONE = 0,         ///< 无手势
    TOUCH_GESTURE_PRESS,            ///< 触摸按下
    TOUCH_GESTURE_RELEASE,          ///< 触摸释放
    TOUCH_GESTURE_CLICK,            ///< 单击手势

    TOUCH_GESTURE_LONG_PRESS,       ///< 长按手势
    TOUCH_GESTURE_MOVE,             ///< 移动手势
    TOUCH_GESTURE_SWIPE_LEFT,       ///< 向左滑动
    TOUCH_GESTURE_SWIPE_RIGHT,      ///< 向右滑动
    TOUCH_GESTURE_SWIPE_UP,         ///< 向上滑动
    TOUCH_GESTURE_SWIPE_DOWN        ///< 向下滑动
} touch_gesture_type_t;

/**
 * @brief 触摸状态机枚举
 * @note  用于触摸状态管理和手势识别
 */
typedef enum
{
    TOUCH_STATE_IDLE = 0,           ///< 空闲状态（无触摸）
    TOUCH_STATE_PRESSED,            ///< 按下状态（刚触摸）
    TOUCH_STATE_MOVING,             ///< 移动状态（触摸移动中）
    TOUCH_STATE_RELEASED            ///< 释放状态（触摸结束）
} touch_state_t;

/**
 * @brief 触摸手势事件结构
 * @note  包含完整的手势事件信息
 */
typedef struct
{
    touch_gesture_type_t gesture;   ///< 手势类型
    uint16_t x;                     ///< 手势中心X坐标
    uint16_t y;                     ///< 手势中心Y坐标
    uint16_t start_x;               ///< 手势起始X坐标
    uint16_t start_y;               ///< 手势起始Y坐标
    uint16_t end_x;                 ///< 手势结束X坐标
    uint16_t end_y;                 ///< 手势结束Y坐标
    uint32_t duration_ms;           ///< 手势持续时间（毫秒）
    uint32_t timestamp_ms;          ///< 手势时间戳（毫秒）
} touch_gesture_event_t;

/**
 * @brief 触摸状态管理结构
 * @note  用于跟踪触摸状态和手势识别
 */
typedef struct
{
    touch_state_t current_state;        ///< 当前触摸状态
    uint16_t press_x;                   ///< 按下时的X坐标
    uint16_t press_y;                   ///< 按下时的Y坐标
    uint16_t current_x;                 ///< 当前X坐标
    uint16_t current_y;                 ///< 当前Y坐标
    uint32_t press_timestamp_ms;        ///< 按下时间戳（毫秒）
    uint32_t release_timestamp_ms;      ///< 释放时间戳（毫秒）

    uint8_t  is_moving;                 ///< 移动状态标志
    uint8_t  long_press_triggered;      ///< 长按触发标志
} touch_state_manager_t;

/**
 * @brief GT1158配置结构
 * @note  包含触摸芯片的配置参数
 */
typedef struct
{
    uint16_t screen_width;              ///< 屏幕宽度（像素）
    uint16_t screen_height;             ///< 屏幕高度（像素）
    screen_orientation_t orientation;   ///< 屏幕方向
    uint8_t  gesture_detection_enable;  ///< 手势检测使能标志
    uint8_t  multi_touch_enable;        ///< 多点触摸使能标志
    uint8_t  is_initialized;            ///< 初始化完成标志
} gt1158_config_t;

/* ========================================================================================
 * 全局变量声明
 * ======================================================================================== */

extern gt1158_config_t g_gt1158_config;            ///< GT1158配置实例
extern gt1158_touch_data_t g_gt1158_touch_data;    ///< GT1158触摸数据实例
extern touch_state_manager_t g_touch_state;        ///< 触摸状态管理实例
extern touch_gesture_event_t g_gesture_event;      ///< 手势事件实例

/* GPIO中断相关全局变量 */
extern volatile uint8_t g_touch_event_flag;        ///< 触摸事件标志（供主循环使用）

/* ========================================================================================
 * 函数声明
 * ======================================================================================== */

/* ========================================================================================
 * 公共API函数声明
 * ======================================================================================== */

/* ----------------------------------------------------------------------------------------
 * 1. 初始化和配置函数
 * ---------------------------------------------------------------------------------------- */

/**
 * @brief  初始化GT1158触摸芯片（一键完整初始化）
 * @note   自动完成所有初始化：硬件复位、设备检测、屏幕配置、中断启用、手势检测
 *         使用默认配置：800x480分辨率、横屏模式、启用中断和手势检测
 * @param  None
 * @retval touch_result_t 初始化结果
 *         - TOUCH_RESULT_OK: 初始化成功
 *         - TOUCH_RESULT_I2C_ERROR: I2C通信失败
 *         - TOUCH_RESULT_ERROR: 其他错误
 */
touch_result_t Touch_Init(void);

/**
 * @brief  高级初始化GT1158触摸芯片（自定义参数）
 * @note   允许用户自定义屏幕参数和功能配置
 * @param  width 屏幕宽度（像素）
 * @param  height 屏幕高度（像素）
 * @param  orientation 屏幕方向
 * @param  enable_gesture 是否启用手势检测（1-启用，0-禁用）
 * @param  enable_interrupt 是否启用GPIO中断（1-启用，0-禁用）
 * @retval touch_result_t 初始化结果
 */
touch_result_t Touch_InitAdvanced(uint16_t width, uint16_t height, screen_orientation_t orientation,
                                  uint8_t enable_gesture, uint8_t enable_interrupt);

/**
 * @brief  配置触摸芯片参数
 * @note   设置屏幕分辨率、方向等参数（通常在初始化后不需要单独调用）
 * @param  width 屏幕宽度（像素）
 * @param  height 屏幕高度（像素）
 * @param  orientation 屏幕方向
 * @retval touch_result_t 配置结果
 */
touch_result_t Touch_Config(uint16_t width, uint16_t height, screen_orientation_t orientation);

/**
 * @brief  根据LCD当前配置重新配置触摸参数
 * @note   当LCD方向发生变化时，调用此函数重新配置触摸参数以保持坐标匹配
 * @param  None
 * @retval touch_result_t 配置结果
 */
touch_result_t Touch_ReconfigureForLCD(void);

/**
 * @brief  获取当前触摸屏幕配置信息
 * @note   返回当前触摸系统的屏幕配置，支持横竖屏动态查询
 * @param  width 输出屏幕宽度指针（可为NULL）
 * @param  height 输出屏幕高度指针（可为NULL）
 * @param  orientation 输出屏幕方向指针（可为NULL）
 * @retval touch_result_t 获取结果
 */
touch_result_t Touch_GetScreenInfo(uint16_t *width, uint16_t *height, screen_orientation_t *orientation);

/* ----------------------------------------------------------------------------------------
 * 2. 触摸数据读取函数
 * ---------------------------------------------------------------------------------------- */

/**
 * @brief  获取单点触摸坐标
 * @note   获取第一个有效触摸点的坐标信息
 * @param  x X坐标指针（输出参数）
 * @param  y Y坐标指针（输出参数）
 * @retval touch_result_t 获取结果
 *         - TOUCH_RESULT_OK: 获取成功
 *         - TOUCH_RESULT_NO_TOUCH: 无触摸数据
 *         - TOUCH_RESULT_INVALID_PARAM: 参数无效
 */
touch_result_t Touch_GetSinglePoint(uint16_t *x, uint16_t *y);

/**
 * @brief  读取多点触摸数据
 * @note   读取所有有效触摸点的完整数据
 * @param  touch_data 触摸数据结构指针（输出参数）
 * @retval touch_result_t 读取结果
 *         - TOUCH_RESULT_OK: 读取成功
 *         - TOUCH_RESULT_INVALID_PARAM: 参数无效
 *         - TOUCH_RESULT_I2C_ERROR: I2C通信错误
 */
touch_result_t Touch_ReadMultiPointData(gt1158_touch_data_t *touch_data);

/* ----------------------------------------------------------------------------------------
 * 触摸事件处理函数
 * ---------------------------------------------------------------------------------------- */

/* 注意：Touch_ProcessEvents 函数已被删除，请使用 Touch_WaitForInput 函数代替 */

/* ----------------------------------------------------------------------------------------
 * 3. 手势检测函数
 * ---------------------------------------------------------------------------------------- */

/**
 * @brief  启用或禁用手势检测功能
 * @note   控制手势识别功能的开关
 * @param  enable 使能标志（1-启用，0-禁用）
 * @retval touch_result_t 设置结果
 */
touch_result_t Touch_SetGestureDetection(uint8_t enable);

/**
 * @brief  获取最新的手势事件
 * @note   获取并清除最新的手势事件数据
 * @param  gesture_event 手势事件结构指针（输出参数）
 * @retval touch_result_t 获取结果
 *         - TOUCH_RESULT_OK: 获取成功
 *         - TOUCH_RESULT_NO_TOUCH: 无手势事件
 *         - TOUCH_RESULT_INVALID_PARAM: 参数无效
 */
touch_result_t Touch_GetGestureEvent(touch_gesture_event_t *gesture_event);

/**
 * @brief  阻塞式等待触摸输入
 * @note   等待检测5种手势：单击、上滑、下滑、左滑、右滑（不包括长按）
 *         函数执行期间会阻塞程序运行，直到有触摸输入或超时
 * @param  timeout 超时时间(毫秒)，TOUCH_WAIT_ALWAYS表示一直等待
 * @retval touch_gesture_type_t 检测到的手势类型（单击或滑动），超时返回TOUCH_GESTURE_NONE
 */
touch_gesture_type_t Touch_WaitForInput(uint32_t timeout);

/* ----------------------------------------------------------------------------------------
 * 4. 坐标处理和验证函数
 * ---------------------------------------------------------------------------------------- */

/**
 * @brief  验证坐标是否在当前屏幕范围内
 * @note   公共函数，用于验证坐标的有效性，支持横竖屏动态切换
 * @param  x X坐标
 * @param  y Y坐标
 * @retval uint8_t 1-坐标有效，0-坐标无效
 */
uint8_t Touch_IsCoordinateInRange(uint16_t x, uint16_t y);

/**
 * @brief  将坐标限制在当前屏幕范围内
 * @note   公共函数，确保坐标不超出屏幕边界，支持横竖屏动态切换
 * @param  x X坐标指针（输入输出参数）
 * @param  y Y坐标指针（输入输出参数）
 * @retval touch_result_t 处理结果
 */
touch_result_t Touch_ClampCoordinates(uint16_t *x, uint16_t *y);

/* ----------------------------------------------------------------------------------------
 * 5. 中断处理函数
 * ---------------------------------------------------------------------------------------- */

/**
 * @brief  使能触摸中断
 * @note   启用触摸芯片的中断功能
 * @param  None
 * @retval None
 */
void Touch_EnableInterrupt(void);

/**
 * @brief  禁用触摸中断
 * @note   关闭触摸芯片的中断功能
 * @param  None
 * @retval None
 */
void Touch_DisableInterrupt(void);

/**
 * @brief  触摸中断处理函数
 * @note   在中断服务程序中调用
 * @param  None
 * @retval None
 */
void Touch_InterruptHandler(void);



/* ----------------------------------------------------------------------------------------
 * 6. 应用层回调函数声明（弱定义，用户可重新实现）
 * ---------------------------------------------------------------------------------------- */

/**
 * @brief  触摸按下事件回调函数
 * @note   当检测到触摸按下时调用，用户可重新实现此函数
 * @param  x X坐标值
 * @param  y Y坐标值
 * @retval None
 */
void Touch_OnPress(uint16_t x, uint16_t y);

/**
 * @brief  触摸释放事件回调函数
 * @note   当检测到触摸释放时调用，用户可重新实现此函数
 * @param  x X坐标值
 * @param  y Y坐标值
 * @retval None
 */
void Touch_OnRelease(uint16_t x, uint16_t y);

/**
 * @brief  触摸移动轨迹绘制回调函数
 * @note   当检测到触摸移动时调用，用于绘制轨迹
 * @param  prev_x 前一个X坐标
 * @param  prev_y 前一个Y坐标
 * @param  curr_x 当前X坐标
 * @param  curr_y 当前Y坐标
 * @param  brush_params 画笔参数（可为NULL）
 * @retval None
 */
void Touch_OnDrawTrail(int16_t prev_x, int16_t prev_y, int16_t curr_x, int16_t curr_y, void *brush_params);

/* ----------------------------------------------------------------------------------------
 * 7. 手势事件回调函数声明（弱定义，用户可重新实现）
 * ---------------------------------------------------------------------------------------- */

/**
 * @brief  单击手势回调函数
 * @param  x 单击位置X坐标
 * @param  y 单击位置Y坐标
 * @retval None
 */
void Touch_OnClick(uint16_t x, uint16_t y);



/**
 * @brief  长按手势回调函数
 * @param  x 长按位置X坐标
 * @param  y 长按位置Y坐标
 * @param  duration_ms 长按持续时间（毫秒）
 * @retval None
 */
void Touch_OnLongPress(uint16_t x, uint16_t y, uint32_t duration_ms);

/**
 * @brief  滑动手势回调函数
 * @param  gesture_type 滑动手势类型
 * @param  start_x 滑动起始X坐标
 * @param  start_y 滑动起始Y坐标
 * @param  end_x 滑动结束X坐标
 * @param  end_y 滑动结束Y坐标
 * @retval None
 */
void Touch_OnSwipe(touch_gesture_type_t gesture_type, uint16_t start_x, uint16_t start_y, uint16_t end_x, uint16_t end_y);

/**
 * @brief  移动手势回调函数
 * @param  x 当前X坐标
 * @param  y 当前Y坐标
 * @param  delta_x X方向移动距离
 * @param  delta_y Y方向移动距离
 * @retval None
 */
void Touch_OnMove(uint16_t x, uint16_t y, int16_t delta_x, int16_t delta_y);



/* ========================================================================================
 * 兼容性宏定义（保持向后兼容）
 * ======================================================================================== */

/* 旧版本函数名兼容性定义 */
#define GT1158_Init()                       Touch_Init()
#define GT1158_GetXY(x, y)                  Touch_GetSinglePoint(x, y)
/* GT1158_TouchProcess() 宏已删除，请使用 Touch_WaitForInput() 函数代替 */
#define GT1158_SetGestureEnable(enable)     Touch_SetGestureDetection(enable)
#define GT1158_ReadData(data)               Touch_ReadMultiPointData(data)
#define GT1158_GetGestureEvent(event)       Touch_GetGestureEvent(event)

/* 兼容性宏定义 */
#define Touch_Button_Down(x, y)             Touch_OnPress(x, y)
#define Touch_Button_Up(x, y)               Touch_OnRelease(x, y)
#define Touch_Click_Callback(x, y)          Touch_OnClick(x, y)

#endif /* INC_TOUCH_H_ */
